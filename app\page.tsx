import Link from 'next/link'
import Image from 'next/image'

export default function Home() {
  return (
    <>
      {/* Hero Banner */}
      <section className="relative bg-gray-50 min-h-[80vh] flex items-center">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8 lg:pr-12">
              <h1 className="text-6xl lg:text-8xl font-light text-black leading-[0.9] tracking-tight">
                NEW SEASON<br />HEROES
              </h1>
              <p className="text-xl text-gray-600 max-w-lg leading-relaxed">
                Discover the latest arrivals from your favorite designers
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/women" className="bg-black text-white px-12 py-4 text-sm font-medium tracking-wide hover:bg-gray-800 transition-colors text-center">
                  SHOP WOMEN
                </Link>
                <Link href="/men" className="border-2 border-black text-black px-12 py-4 text-sm font-medium tracking-wide hover:bg-black hover:text-white transition-colors text-center">
                  SHOP MEN
                </Link>
              </div>
            </div>
            <div className="relative h-[500px] lg:h-[700px]">
              <Image
                src="/hero-image.jpg"
                alt="New Season Heroes"
                fill
                className="object-cover rounded-lg"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-24">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-light text-black mb-8 tracking-wide">SHOP BY CATEGORY</h2>
            <div className="w-20 h-px bg-black mx-auto"></div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 lg:gap-8">
            {[
              { name: 'CLOTHING', count: '545 PRODUCTS', image: '/category-clothing.jpg' },
              { name: 'SHOES', count: '234 PRODUCTS', image: '/category-shoes.jpg' },
              { name: 'BAGS', count: '156 PRODUCTS', image: '/category-bags.jpg' },
              { name: 'ACCESSORIES', count: '89 PRODUCTS', image: '/category-accessories.jpg' },
              { name: 'SALE', count: 'UP TO 70% OFF', image: '/category-sale.jpg' }
            ].map((category) => (
              <Link key={category.name} href={`/${category.name.toLowerCase()}`} className="group text-center hover-lift">
                <div className="relative aspect-[4/5] mb-6 overflow-hidden bg-gray-50 rounded-sm">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                </div>
                <h3 className="text-sm font-semibold text-black mb-2 tracking-wide group-hover:text-gray-600 transition-colors">{category.name}</h3>
                <p className="text-xs text-gray-500 uppercase tracking-wide">{category.count}</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Editorial Content */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <div className="relative aspect-[3/4] group overflow-hidden rounded-sm">
              <Image
                src="/editorial-1.jpg"
                alt="Editorial Content"
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              <div className="absolute bottom-16 left-16 text-white">
                <h3 className="text-4xl font-light mb-4 tracking-wide">WINTER ESSENTIALS</h3>
                <p className="text-lg mb-8 opacity-90">Discover our curated edit</p>
                <Link href="/winter-essentials" className="text-sm font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all">
                  SHOP NOW
                </Link>
              </div>
            </div>
            <div className="space-y-8">
              <div className="relative aspect-[4/3] group overflow-hidden rounded-sm">
                <Image
                  src="/editorial-2.jpg"
                  alt="Editorial Content"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-12 left-12 text-white">
                  <h3 className="text-3xl font-light mb-4 tracking-wide">THE GOLDEN HOUR</h3>
                  <Link href="/golden-hour" className="text-sm font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all">
                    DISCOVER
                  </Link>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-6">
                {[
                  { name: 'DRESSES', image: '/mini-1.jpg' },
                  { name: 'KNITWEAR', image: '/mini-2.jpg' },
                  { name: 'OUTERWEAR', image: '/mini-3.jpg' }
                ].map((item) => (
                  <Link key={item.name} href={`/${item.name.toLowerCase()}`} className="group text-center">
                    <div className="relative aspect-[4/5] mb-4 overflow-hidden bg-gray-100 rounded-sm">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <p className="text-xs font-semibold tracking-wide text-black">{item.name}</p>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-24 bg-black text-white">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-5xl font-light mb-8 tracking-wide">SIGN UP TO OUR EMAILS</h2>
          <p className="text-gray-300 mb-16 max-w-2xl mx-auto text-xl leading-relaxed">
            Be the first to know about new arrivals, exclusive offers and style inspiration
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-8 py-5 bg-transparent border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:border-white text-lg"
            />
            <button className="bg-white text-black px-12 py-5 font-semibold tracking-wide hover:bg-gray-100 transition-colors text-sm">
              SIGN UP
            </button>
          </div>
        </div>
      </section>
    </>
  )
}