
@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  /* Colors */
  --color-black: #000000;
  --color-white: #ffffff;
  --color-gray-50: #fafafa;
  --color-gray-100: #f5f5f5;
  --color-gray-200: #e5e5e5;
  --color-gray-300: #d4d4d4;
  --color-gray-400: #a3a3a3;
  --color-gray-500: #737373;
  --color-gray-600: #525252;
  --color-gray-700: #404040;
  --color-gray-800: #262626;
  --color-gray-900: #171717;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Typography */
  --font-body: 'Inter', system-ui, -apple-system, sans-serif;
  --font-display: 'Playfair Display', serif;

  /* Layout */
  --max-width: 1280px;
  --container-padding: 1rem;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-body);
  line-height: 1.6;
  font-size: 16px;
}

body {
  color: var(--color-black);
  background: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.01em;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Focus states */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid rgba(0, 0, 0, 0.8);
  outline-offset: 2px;
}

/* Improved focus for interactive elements */
.btn:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Improved hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced mobile touch targets */
@media (max-width: 768px) {
  button, 
  a[role="button"],
  .touchable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Improved form elements */
input[type="text"],
input[type="email"],
input[type="search"],
textarea {
  appearance: none;
  -webkit-appearance: none;
}

/* ==========================================================================
   LAYOUT SYSTEM
   ========================================================================== */

/* Container System */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}

/* Section Spacing */
.section {
  padding: 3rem 0;
}

@media (min-width: 768px) {
  .section {
    padding: 4rem 0;
  }
}

@media (min-width: 1024px) {
  .section {
    padding: 5rem 0;
  }
}

.section-alt {
  background-color: var(--color-gray-50);
}

/* Page Layout */
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1;
  padding-top: 1rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  .page-content {
    padding-top: 1.5rem;
    padding-bottom: 3rem;
  }
}

/* ==========================================================================
   TYPOGRAPHY SYSTEM
   ========================================================================== */

.hero-title {
  font-family: var(--font-display);
  font-size: 2.5rem;
  font-weight: 400;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: var(--color-black);
  margin-bottom: var(--space-lg);
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 4rem;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 5rem;
  }
}

.section-title {
  font-family: var(--font-display);
  font-size: 2rem;
  font-weight: 400;
  line-height: 1.3;
  color: var(--color-black);
  margin-bottom: var(--space-2xl);
  text-align: center;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 2.5rem;
  }
}

/* ==========================================================================
   BUTTON SYSTEM
   ========================================================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-xl);
  font-family: var(--font-body);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  min-height: 48px;
  min-width: 120px;
}

.btn-primary {
  background-color: var(--color-black);
  color: var(--color-white);
  border: 2px solid var(--color-black);
}

.btn-primary:hover {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-800);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-black);
  border: 2px solid var(--color-black);
}

.btn-secondary:hover {
  background-color: var(--color-black);
  color: var(--color-white);
}

/* ==========================================================================
   HERO SECTION
   ========================================================================== */

.hero-section {
  padding: var(--space-3xl) 0;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
  min-height: 60vh;
  display: flex;
  align-items: center;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-description {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--color-gray-600);
  margin-bottom: var(--space-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .hero-description {
    font-size: 1.25rem;
  }
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  align-items: center;
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* ==========================================================================
   PRODUCT GRID SYSTEM
   ========================================================================== */

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-xl);
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
}

@media (max-width: 480px) {
  .products-grid {
    gap: var(--space-sm);
  }
}

/* ==========================================================================
   NEWSLETTER SECTION
   ========================================================================== */

.newsletter-section {
  background-color: var(--color-black);
  color: var(--color-white);
  padding: var(--space-3xl) 0;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-title {
  font-family: var(--font-display);
  font-size: 2rem;
  font-weight: 400;
  margin-bottom: var(--space-md);
}

@media (min-width: 768px) {
  .newsletter-title {
    font-size: 2.5rem;
  }
}

.newsletter-description {
  font-size: 1.125rem;
  color: var(--color-gray-300);
  margin-bottom: var(--space-xl);
  line-height: 1.6;
}

.newsletter-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  max-width: 400px;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .newsletter-form {
    flex-direction: row;
  }
}

.newsletter-input {
  flex: 1;
  padding: var(--space-md);
  border: 2px solid var(--color-gray-600);
  background-color: transparent;
  color: var(--color-white);
  font-size: 0.875rem;
}

.newsletter-input::placeholder {
  color: var(--color-gray-400);
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--color-white);
}

.newsletter-button {
  padding: var(--space-md) var(--space-xl);
  background-color: var(--color-white);
  color: var(--color-black);
  border: none;
  font-weight: 500;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.2s ease;
}

.newsletter-button:hover {
  background-color: var(--color-gray-100);
}

/* ==========================================================================
   PRODUCT CARD SYSTEM
   ========================================================================== */

.product-card {
  background-color: var(--color-white);
  border: 1px solid transparent;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.product-image-container {
  position: relative;
  aspect-ratio: 3/4;
  background-color: var(--color-gray-50);
  overflow: hidden;
}

.product-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image-container img {
  transform: scale(1.05);
}

.sale-badge {
  position: absolute;
  top: var(--space-md);
  left: var(--space-md);
  background-color: var(--color-black);
  color: var(--color-white);
  padding: var(--space-xs) var(--space-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
}

.product-info {
  padding: var(--space-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: 0.75rem;
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--space-xs);
  font-weight: 500;
}

.product-name {
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--color-black);
  line-height: 1.4;
  margin-bottom: var(--space-md);
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.price-container {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-top: auto;
}

.current-price {
  font-weight: 600;
  color: var(--color-black);
  font-size: 0.875rem;
}

.original-price {
  font-size: 0.75rem;
  color: var(--color-gray-400);
  text-decoration: line-through;
}

/* ==========================================================================
   CATEGORY CAROUSEL
   ========================================================================== */

.category-carousel {
  margin-top: var(--space-xl);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-xl);
  max-width: 800px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
}

.category-item {
  display: block;
  text-align: center;
  transition: transform 0.3s ease;
}

.category-item:hover {
  transform: translateY(-4px);
}

.category-image {
  position: relative;
  aspect-ratio: 1;
  background-color: var(--color-gray-100);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: var(--space-md);
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-item:hover .category-image img {
  transform: scale(1.05);
}

.category-name {
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-black);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ==========================================================================
   HEADER SYSTEM
   ========================================================================== */

/* Header styles are now inline in component */

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
}

/* Header Navigation */
.header-nav {
  display: none;
}

@media (min-width: 1024px) {
  .header-nav {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
  }
}

.header-nav-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  font-size: 13px;
  font-weight: 500;
  color: var(--color-black);
  text-decoration: none;
  transition: color 0.2s ease;
  letter-spacing: 0.5px;
}

.header-nav-item:hover {
  color: var(--color-gray-600);
}

.header-nav-item.sale {
  color: #dc2626;
  font-weight: 600;
}

/* Header Icons */
.header-icons {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--color-black);
  border-radius: 50%;
  transition: background-color 0.2s ease;
  position: relative;
}

.header-icon:hover {
  background-color: var(--color-gray-100);
}

.header-icon-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #dc2626;
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--color-black);
  border: none;
  background: none;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

@media (min-width: 1024px) {
  .mobile-menu-button {
    display: none;
  }
}

.mobile-menu-button:hover {
  background-color: var(--color-gray-100);
}

/* ==========================================================================
   MEGA MENU (DESKTOP)
   ========================================================================== */

.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 40;
}

.mega-menu-content {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: var(--space-2xl) var(--space-xl);
}

.mega-menu-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--space-2xl);
}

.mega-menu-category {
  display: flex;
  flex-direction: column;
}

.mega-menu-category-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-black);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-xs);
  border-bottom: 1px solid var(--color-gray-200);
}

.mega-menu-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.mega-menu-item {
  font-size: 0.875rem;
  color: var(--color-gray-600);
  text-decoration: none;
  transition: color 0.2s ease;
  padding: var(--space-xs) 0;
}

.mega-menu-item:hover {
  color: var(--color-black);
}

/* ==========================================================================
   MOBILE MENU
   ========================================================================== */

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 40;
  max-height: 80vh;
  overflow-y: auto;
}

@media (min-width: 1024px) {
  .mobile-menu {
    display: none;
  }
}

.mobile-menu-content {
  padding: var(--space-lg) var(--space-md);
}

.mobile-menu-section {
  margin-bottom: var(--space-lg);
}

.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: var(--space-md) 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-black);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
}

.mobile-menu-toggle:hover {
  color: var(--color-gray-600);
}

.mobile-menu-toggle-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.mobile-menu-toggle-icon.expanded {
  transform: rotate(180deg);
}

.mobile-menu-submenu {
  padding-left: var(--space-lg);
  margin-top: var(--space-sm);
}

.mobile-menu-item {
  display: block;
  padding: var(--space-sm) 0;
  font-size: 0.875rem;
  color: var(--color-gray-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

.mobile-menu-item:hover {
  color: var(--color-black);
}

.mobile-menu-divider {
  height: 1px;
  background-color: var(--color-gray-200);
  margin: var(--space-lg) 0;
}

/* Performance optimizations */
img {
  content-visibility: auto;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    border: 2px solid white;
  }

  .btn-secondary {
    border-width: 3px;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
