
'use client'

import Link from 'next/link'
import { useState, useRef, useEffect } from 'react'
import { 
  MagnifyingGlassIcon, 
  HeartIcon, 
  ShoppingBagIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

const megaMenuItems = {
  women: {
    categories: [
      {
        name: 'Clothing',
        items: ['Shop All', 'Dresses', 'Knitwear', 'Coats', 'Jackets', 'Jeans', 'Jumpsuits', 'Lingerie', 'Skirts', 'Suits', 'Swimwear', 'Tops', 'Trousers']
      },
      {
        name: 'Shoes',
        items: ['Shop All', 'Boots', 'Flats', 'Heels', 'Sandals', 'Sneakers', 'Trainers']
      },
      {
        name: 'Bags',
        items: ['Shop All', 'Backpacks', 'Belt Bags', 'Clutches', 'Cross-body', 'Handbags', 'Shoulder Bags', 'Totes', 'Travel']
      },
      {
        name: 'Accessories',
        items: ['Shop All', 'Belts', 'Gloves', 'Hair Accessories', 'Hats', 'Jewelry & Watches', 'Scarves', 'Sunglasses', 'Tech Accessories']
      },
      {
        name: 'Designers',
        items: ['Acne Studios', 'Bottega Veneta', 'Ganni', 'Jacquemus', 'Khaite', 'Loewe', 'Prada', 'Saint Laurent', 'The Row', 'Toteme']
      }
    ]
  },
  men: {
    categories: [
      {
        name: 'Clothing',
        items: ['Shop All', 'Blazers', 'Coats', 'Hoodies', 'Jackets', 'Jeans', 'Knitwear', 'Polo Shirts', 'Shirts', 'Shorts', 'Suits', 'Sweatshirts', 'T-Shirts', 'Trousers']
      },
      {
        name: 'Shoes',
        items: ['Shop All', 'Boots', 'Dress Shoes', 'Loafers', 'Sandals', 'Sneakers', 'Trainers']
      },
      {
        name: 'Bags',
        items: ['Shop All', 'Backpacks', 'Belt Bags', 'Briefcases', 'Cross-body', 'Messenger', 'Totes', 'Travel']
      },
      {
        name: 'Accessories',
        items: ['Shop All', 'Belts', 'Gloves', 'Hats', 'Jewelry & Watches', 'Scarves', 'Sunglasses', 'Tech Accessories', 'Ties', 'Wallets']
      },
      {
        name: 'Designers',
        items: ['Acne Studios', 'Bottega Veneta', 'Fear of God', 'Jacquemus', 'Kenzo', 'Off-White', 'Stone Island', 'Thom Browne', 'The Row', 'Valentino']
      }
    ]
  }
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [activeMegaMenu, setActiveMegaMenu] = useState<string | null>(null)
  const [expandedMobileMenu, setExpandedMobileMenu] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const megaMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target as Node)) {
        setActiveMegaMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleMegaMenuEnter = (menu: string) => {
    if (!isMobile) {
      setActiveMegaMenu(menu)
    }
  }

  const handleMegaMenuLeave = () => {
    if (!isMobile) {
      setActiveMegaMenu(null)
    }
  }

  const toggleMobileSubmenu = (menu: string) => {
    setExpandedMobileMenu(expandedMobileMenu === menu ? null : menu)
  }

  return (
    <>
      {/* Main Header */}
      <header className="bg-[#c8e6c9] border-b border-[#a5d6a7] sticky top-0 z-50" ref={megaMenuRef}>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-black hover:text-gray-600 transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <XMarkIcon className="h-5 w-5" /> : <Bars3Icon className="h-5 w-5" />}
            </button>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0 lg:mx-0">
              <h1 className="text-xl md:text-2xl font-bold tracking-[0.2em] text-black">MATCHES</h1>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-6">
              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('women')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/women" className="text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center">
                  Women
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('men')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/men" className="text-sm font-medium text-black hover:text-gray-600 transition-colors flex items-center">
                  Men
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <Link href="/designers" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Designers
              </Link>
              <Link href="/clothing" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Clothing
              </Link>
              <Link href="/shoes" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Shoes
              </Link>
              <Link href="/bags" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Bags
              </Link>
              <Link href="/accessories" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Accessories
              </Link>
              <Link href="/jewellery-watches" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Jewellery & Watches
              </Link>
              <Link href="/home" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Home
              </Link>
              <Link href="/kids" className="text-sm font-medium text-black hover:text-gray-600 transition-colors">
                Kids
              </Link>
              <Link href="/outlet" className="text-sm font-medium text-red-600 hover:text-red-700 transition-colors">
                Outlet
              </Link>
            </nav>

            {/* Right side icons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="p-2 text-black hover:text-gray-600 transition-colors lg:flex"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
              </button>

              <Link
                href="/account"
                className="p-2 text-black hover:text-gray-600 transition-colors hidden lg:flex"
                aria-label="Account"
              >
                <UserIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/wishlist"
                className="p-2 text-black hover:text-gray-600 transition-colors relative hidden lg:flex"
                aria-label="Wishlist"
              >
                <HeartIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/bag"
                className="p-2 text-black hover:text-gray-600 transition-colors relative"
                aria-label="Shopping bag"
              >
                <ShoppingBagIcon className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          {isSearchOpen && (
            <div className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 py-4 z-50">
              <div className="container">
                <div className="flex items-center gap-4">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="Search for products, designers..."
                      className="w-full px-4 py-3 border border-gray-300 bg-white text-black focus:outline-none focus:border-black text-sm"
                      autoFocus
                    />
                    <MagnifyingGlassIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                  <button
                    onClick={() => setIsSearchOpen(false)}
                    className="text-sm font-medium hover:text-gray-600 text-black px-4"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Mega Menu */}
        {activeMegaMenu && megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems] && (
          <div
            className="absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40"
            onMouseEnter={() => setActiveMegaMenu(activeMegaMenu)}
            onMouseLeave={handleMegaMenuLeave}
          >
            <div className="container mx-auto px-4 py-8">
              <div className="grid grid-cols-5 gap-8">
                {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].categories.map((category) => (
                  <div key={category.name} className="space-y-4">
                    <h3 className="text-sm font-semibold text-black uppercase tracking-wide">
                      {category.name}
                    </h3>
                    <div className="space-y-2">
                      {category.items.map((item, index) => (
                        <Link
                          key={`${category.name}-${item}-${index}`}
                          href={`/${activeMegaMenu}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                          className="block text-sm text-gray-600 hover:text-black transition-colors"
                        >
                          {item}
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40">
            <div className="container mx-auto px-4 py-6">
              {/* Women Menu */}
              <div className="border-b border-gray-200 pb-4 mb-4">
                <button
                  onClick={() => toggleMobileSubmenu('women')}
                  className="flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black"
                >
                  Women
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'women' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'women' && (
                  <div className="pl-4 space-y-2 mt-2">
                    <Link href="/women/new-in" className="block py-2 text-sm text-gray-600 hover:text-black">New In</Link>
                    <Link href="/women/clothing" className="block py-2 text-sm text-gray-600 hover:text-black">Clothing</Link>
                    <Link href="/women/shoes" className="block py-2 text-sm text-gray-600 hover:text-black">Shoes</Link>
                    <Link href="/women/bags" className="block py-2 text-sm text-gray-600 hover:text-black">Bags</Link>
                    <Link href="/women/accessories" className="block py-2 text-sm text-gray-600 hover:text-black">Accessories</Link>
                  </div>
                )}
              </div>

              {/* Men Menu */}
              <div className="border-b border-gray-200 pb-4 mb-4">
                <button
                  onClick={() => toggleMobileSubmenu('men')}
                  className="flex items-center justify-between w-full text-left py-3 text-sm font-medium text-black"
                >
                  Men
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'men' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'men' && (
                  <div className="pl-4 space-y-2 mt-2">
                    <Link href="/men/new-in" className="block py-2 text-sm text-gray-600 hover:text-black">New In</Link>
                    <Link href="/men/clothing" className="block py-2 text-sm text-gray-600 hover:text-black">Clothing</Link>
                    <Link href="/men/shoes" className="block py-2 text-sm text-gray-600 hover:text-black">Shoes</Link>
                    <Link href="/men/bags" className="block py-2 text-sm text-gray-600 hover:text-black">Bags</Link>
                    <Link href="/men/accessories" className="block py-2 text-sm text-gray-600 hover:text-black">Accessories</Link>
                  </div>
                )}
              </div>

              <div className="space-y-3 border-b border-gray-200 pb-4 mb-4">
                <Link href="/just-in" className="block py-2 text-sm font-medium text-black">Just In</Link>
                <Link href="/designers" className="block py-2 text-sm font-medium text-black">Designers</Link>
                <Link href="/sale" className="block py-2 text-sm font-medium text-red-600">Sale</Link>
              </div>

              <div className="space-y-3">
                <Link href="/account" className="flex items-center py-2 text-sm font-medium text-black">
                  <UserIcon className="h-4 w-4 mr-3" />
                  My Account
                </Link>
                <Link href="/wishlist" className="flex items-center py-2 text-sm font-medium text-black">
                  <HeartIcon className="h-4 w-4 mr-3" />
                  Wishlist
                </Link>
              </div>
            </div>
          </div>
        )}
      </header>
    </>
  )
}
