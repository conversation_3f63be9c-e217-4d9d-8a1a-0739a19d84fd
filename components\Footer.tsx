
export default function Footer() {
  return (
    <footer className="bg-gray-50 border-t border-gray-200 text-gray-600 text-sm py-20 mt-auto">
      <div className="max-w-7xl mx-auto px-4 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 lg:gap-12 mb-16">
          {/* MATCHES */}
          <div>
            <h4 className="font-semibold mb-6 text-black text-sm uppercase tracking-wide">MATCHES</h4>
            <ul className="space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">About Us</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Careers</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Affiliates</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Press</a></li>
            </ul>
          </div>

          {/* Customer Care */}
          <div>
            <h4 className="font-semibold mb-6 text-black text-sm uppercase tracking-wide">Customer Care</h4>
            <ul className="space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Contact Us</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Size Guide</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Delivery</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Returns</a></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-semibold mb-6 text-black text-sm uppercase tracking-wide">Services</h4>
            <ul className="space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Personal Shopping</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Gift Cards</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Loyalty Program</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Style Advice</a></li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="font-semibold mb-6 text-black text-sm uppercase tracking-wide">Legal</h4>
            <ul className="space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Terms & Conditions</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Privacy Policy</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Cookie Policy</a></li>
            </ul>
          </div>

          {/* Connect */}
          <div>
            <h4 className="font-semibold mb-6 text-black text-sm uppercase tracking-wide">Connect</h4>
            <ul className="space-y-4">
              <li><a href="#" className="hover:text-black transition-colors text-sm">Instagram</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Twitter</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Facebook</a></li>
              <li><a href="#" className="hover:text-black transition-colors text-sm">Pinterest</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-gray-200 pt-12">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6 text-center lg:text-left">
            <div className="text-xs text-gray-500">
              © 2024 MATCHES. All rights reserved.
            </div>

            <div className="text-xs text-gray-500">
              Shipping worldwide | Customer service: +44 (0)20 7647 8888
            </div>

            <div className="flex items-center gap-6">
              <span className="text-xs text-gray-500">Download our app:</span>
              <div className="flex gap-3">
                <a href="#" className="bg-black text-white px-4 py-2 text-xs hover:bg-gray-800 transition-colors rounded">App Store</a>
                <a href="#" className="bg-black text-white px-4 py-2 text-xs hover:bg-gray-800 transition-colors rounded">Google Play</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
